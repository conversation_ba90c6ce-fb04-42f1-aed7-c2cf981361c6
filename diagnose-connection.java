import java.io.*;
import java.net.*;
import java.nio.charset.StandardCharsets;

public class DiagnoseConnection {
    public static void main(String[] args) {
        System.out.println("=== 连接诊断工具 ===");
        
        // 1. 检查端口是否开放
        System.out.println("1. 检查端口3001是否开放...");
        try (Socket socket = new Socket()) {
            socket.connect(new InetSocketAddress("localhost", 3001), 5000);
            System.out.println("✓ 端口3001可以连接");
        } catch (IOException e) {
            System.out.println("✗ 端口3001连接失败: " + e.getMessage());
            return;
        }
        
        // 2. 测试HTTP连接
        System.out.println("2. 测试HTTP连接...");
        try {
            URL url = new URL("http://localhost:3001/api/health");
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(5000);
            connection.setReadTimeout(10000);
            connection.setRequestProperty("Accept", "application/json");
            
            int responseCode = connection.getResponseCode();
            System.out.println("HTTP响应码: " + responseCode);
            
            // 读取响应
            StringBuilder response = new StringBuilder();
            try (BufferedReader br = new BufferedReader(new InputStreamReader(
                    responseCode >= 200 && responseCode < 300 
                        ? connection.getInputStream() 
                        : connection.getErrorStream(), 
                    StandardCharsets.UTF_8))) {
                
                String responseLine;
                while ((responseLine = br.readLine()) != null) {
                    response.append(responseLine.trim());
                }
            }
            
            System.out.println("响应内容: " + response.toString());
            System.out.println("✓ HTTP连接成功");
            
        } catch (Exception e) {
            System.out.println("✗ HTTP连接失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        // 3. 测试POST请求
        System.out.println("3. 测试POST请求...");
        try {
            URL url = new URL("http://localhost:3001/api/stream/start");
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            
            connection.setRequestMethod("POST");
            connection.setConnectTimeout(5000);
            connection.setReadTimeout(10000);
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("Accept", "application/json");
            connection.setDoOutput(true);
            
            // 发送测试数据
            String testData = "{\"rtspUrl\":\"rtsp://test\",\"streamId\":\"test\",\"format\":\"hls\"}";
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = testData.getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            }
            
            int responseCode = connection.getResponseCode();
            System.out.println("POST响应码: " + responseCode);
            
            // 读取响应
            StringBuilder response = new StringBuilder();
            try (BufferedReader br = new BufferedReader(new InputStreamReader(
                    responseCode >= 200 && responseCode < 300 
                        ? connection.getInputStream() 
                        : connection.getErrorStream(), 
                    StandardCharsets.UTF_8))) {
                
                String responseLine;
                while ((responseLine = br.readLine()) != null) {
                    response.append(responseLine.trim());
                }
            }
            
            System.out.println("POST响应内容: " + response.toString());
            System.out.println("✓ POST请求成功");
            
        } catch (Exception e) {
            System.out.println("✗ POST请求失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("=== 诊断完成 ===");
    }
}
