# 连接问题排查指南

## 问题描述
Java应用在尝试启动摄像头流时出现 `Connection refused: getsockopt` 错误，表明无法连接到Node.js流服务器。

## 解决步骤

### 1. 确认Node.js流服务器已启动

**检查服务器状态：**
```bash
# 检查端口3001是否被占用
netstat -ano | findstr :3001

# 或者使用PowerShell
Get-NetTCPConnection -LocalPort 3001
```

**启动流服务器：**
```bash
# 方法1：使用启动脚本
.\start-stream-server.ps1

# 方法2：直接启动
node stream-server.js
```

**预期输出：**
```
EduFusionCenter RTSP转码服务器运行在端口 3001
✓ FFmpeg 可用
```

### 2. 验证服务器连接

**在浏览器中测试：**
- 访问：http://localhost:3001/test.html
- 点击"测试健康检查"按钮
- 应该看到成功响应

**使用curl测试：**
```bash
curl http://localhost:3001/api/health
```

**预期响应：**
```json
{
  "success": true,
  "message": "Service is healthy",
  "ffmpeg": "available"
}
```

### 3. 检查防火墙设置

**Windows防火墙：**
1. 打开Windows Defender防火墙
2. 点击"允许应用或功能通过Windows Defender防火墙"
3. 确保Node.js被允许通过防火墙
4. 或者临时关闭防火墙进行测试

**端口检查：**
```powershell
# 检查端口是否被其他程序占用
netstat -ano | findstr :3001
```

### 4. 修改配置（如果需要）

**更改端口（如果3001被占用）：**

1. 修改 `stream-server.js`：
```javascript
const PORT = process.env.PORT || 3002; // 改为3002
```

2. 修改 `VideoStreamServiceImpl.java`：
```java
private static final String STREAM_SERVER_URL = "http://localhost:3002";
```

### 5. 调试Java连接

**查看详细日志：**
- 重新启动Java应用
- 尝试启动摄像头流
- 查看控制台输出的详细调试信息

**预期日志输出：**
```
开始启动摄像头流: 10
检查流服务器健康状态: http://localhost:3001/api/health
发送HTTP请求: GET http://localhost:3001/api/health
连接超时: 5000ms, 读取超时: 10000ms
HTTP响应码: 200
HTTP响应内容: {"success":true,"message":"Service is healthy","ffmpeg":"available"}
流服务器健康状态: 健康
```

### 6. 常见问题解决

**问题1：Node.js依赖未安装**
```bash
npm install
```

**问题2：FFmpeg未安装**
- 下载FFmpeg：https://ffmpeg.org/download.html
- 添加到系统PATH
- 验证：`ffmpeg -version`

**问题3：端口冲突**
- 更改端口号（见步骤4）
- 或停止占用端口的其他程序

**问题4：网络配置问题**
- 尝试使用127.0.0.1代替localhost
- 检查hosts文件配置

### 7. 验证完整流程

**完整测试步骤：**

1. **启动Node.js服务器**
   ```bash
   node stream-server.js
   ```

2. **验证服务器响应**
   - 浏览器访问：http://localhost:3001/test.html
   - 所有测试应该通过

3. **启动Java应用**
   - 确保应用正常启动
   - 没有连接错误

4. **测试摄像头流**
   - 进入摄像头管理页面
   - 选择一个摄像头
   - 点击"启动视频流"
   - 查看控制台日志

### 8. 成功标志

**Node.js服务器正常运行：**
- 端口3001被监听
- 健康检查返回成功
- 测试页面所有功能正常

**Java应用连接成功：**
- 健康检查通过
- 能够发送启动流请求
- 收到正确的响应

**视频流功能正常：**
- 摄像头流可以启动
- HLS文件正常生成
- 浏览器可以播放视频

## 联系支持

如果按照以上步骤仍然无法解决问题，请：

1. 收集完整的错误日志
2. 记录具体的操作步骤
3. 提供系统环境信息（操作系统、Node.js版本、Java版本）
4. 参考项目文档获取更多帮助

---

**记住：Node.js流服务器必须在Java应用尝试连接之前启动！**
