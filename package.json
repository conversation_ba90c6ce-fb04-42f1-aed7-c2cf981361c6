{"name": "edufusioncenter-stream", "version": "1.0.0", "description": "EduFusionCenter RTSP视频流转码服务", "main": "stream-server.js", "scripts": {"start": "node stream-server.js", "dev": "nodemon stream-server.js", "install-ffmpeg": "npm run check-ffmpeg"}, "dependencies": {"cors": "^2.8.5", "express": "^4.21.2", "fluent-ffmpeg": "^2.1.3", "ws": "^8.18.2"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["rtsp", "video", "streaming", "ffmpeg", "web-player", "edufusioncenter"], "author": "EduFusionCenter Team", "license": "MIT"}