<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<jsp:include page="/WEB-INF/views/common/layout.jsp">
    <jsp:param name="title" value="摄像头管理" />
    <jsp:param name="content" value="/WEB-INF/views/camera/content.jsp" />
    <jsp:param name="additionalStyles" value="
        /* 摄像头卡片样式 */
        .camera-card {
            border-radius: 16px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.05);
            margin-bottom: 25px;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            border: none;
            overflow: hidden;
            position: relative;
        }
        .camera-card:hover {
            box-shadow: 0 15px 30px rgba(0,0,0,0.1);
            transform: translateY(-5px);
        }
        .camera-card .card-header {
            border-bottom: none;
            padding: 1.25rem 1.5rem;
            background: linear-gradient(to right, #f8f9fa, #ffffff);
        }
        .camera-card .card-body {
            padding: 1.5rem;
        }
        .camera-card .card-footer {
            background: transparent;
            border-top: 1px solid rgba(0,0,0,0.05);
            padding: 1.25rem 1.5rem;
        }
        .camera-status {
            width: 14px;
            height: 14px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
            position: relative;
        }
        .camera-status::after {
            content: '';
            position: absolute;
            top: -3px;
            left: -3px;
            right: -3px;
            bottom: -3px;
            border-radius: 50%;
            animation: pulse 2s infinite;
            opacity: 0;
        }
        .camera-online {
            background-color: #28a745;
            box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.2);
        }
        .camera-online::after {
            background-color: rgba(40, 167, 69, 0.3);
        }
        .camera-offline {
            background-color: #dc3545;
            box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.2);
        }
        .camera-offline::after {
            background-color: rgba(220, 53, 69, 0.3);
        }
        @keyframes pulse {
            0% {
                transform: scale(0.8);
                opacity: 0.8;
            }
            70% {
                transform: scale(1.5);
                opacity: 0;
            }
            100% {
                transform: scale(1.5);
                opacity: 0;
            }
        }
        .camera-thumbnail {
            height: 180px;
            background-color: #f8f9fa;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            position: relative;
            box-shadow: inset 0 0 10px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        .camera-card:hover .camera-thumbnail {
            box-shadow: inset 0 0 15px rgba(0,0,0,0.1);
        }
        .camera-thumbnail img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: all 0.5s ease;
        }
        .camera-card:hover .camera-thumbnail img {
            transform: scale(1.05);
        }
        .camera-thumbnail .camera-icon {
            font-size: 3.5rem;
            color: #adb5bd;
            opacity: 0.7;
            transition: all 0.3s ease;
        }
        .camera-card:hover .camera-thumbnail .camera-icon {
            opacity: 1;
            transform: scale(1.1);
        }
        .camera-info-item {
            display: flex;
            align-items: center;
            margin-bottom: 0.75rem;
            padding: 0.5rem;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        .camera-card:hover .camera-info-item {
            background-color: rgba(0,0,0,0.02);
        }
        .camera-info-item i {
            width: 24px;
            color: #6c757d;
            margin-right: 10px;
            text-align: center;
        }
        .camera-info-item span {
            flex: 1;
        }
        .btn-camera-action {
            border-radius: 50px;
            padding: 0.5rem 1.25rem;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .btn-camera-action:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 10px rgba(0,0,0,0.1);
        }
        .btn-camera-action i {
            margin-right: 5px;
        }
        /* 统计卡片样式 */
        .stats-card {
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 10px 20px rgba(0,0,0,0.05);
            position: relative;
            border: none;
            transition: all 0.4s ease;
            height: 100%;
        }
        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.1);
        }
        .stats-card .card-body {
            padding: 2rem;
            position: relative;
            z-index: 1;
        }
        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            opacity: 0.8;
            z-index: 0;
        }
        .stats-card.bg-primary::before {
            background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
        }
        .stats-card.bg-success::before {
            background: linear-gradient(135deg, #1cc88a 0%, #13855c 100%);
        }
        .stats-card.bg-danger::before {
            background: linear-gradient(135deg, #e74a3b 0%, #be2617 100%);
        }
        .stats-icon-container {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            background-color: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }
        .stats-card:hover .stats-icon-container {
            transform: scale(1.1) rotate(10deg);
            background-color: rgba(255,255,255,0.3);
        }
        .stats-icon {
            font-size: 2.5rem;
            color: white;
        }
        .stats-card h3 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        .stats-card p {
            font-size: 1rem;
            opacity: 0.8;
        }
        .stats-trend {
            position: absolute;
            bottom: 1.5rem;
            right: 1.5rem;
            font-size: 0.875rem;
            display: flex;
            align-items: center;
        }
        .stats-trend i {
            margin-right: 5px;
        }
        /* 筛选工具栏 */
        .filter-toolbar {
            background-color: #f8f9fa;
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        .filter-label {
            font-weight: 500;
            margin-right: 10px;
            color: #495057;
        }
        .filter-btn {
            border-radius: 20px;
            margin-right: 5px;
            font-size: 0.875rem;
            padding: 0.375rem 1rem;
            background-color: #fff;
            border: 1px solid #dee2e6;
            color: #495057;
            transition: all 0.2s ease;
        }
        .filter-btn:hover {
            background-color: #e9ecef;
        }
        .filter-btn.active {
            background-color: #007bff;
            border-color: #007bff;
            color: #fff;
        }
        /* 模态框样式 */
        .modal-content {
            border-radius: 16px;
            border: none;
            box-shadow: 0 15px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .modal-header {
            background: linear-gradient(to right, #f8f9fa, #ffffff);
            border-bottom: 1px solid rgba(0,0,0,0.05);
            padding: 1.5rem;
        }
        .modal-body {
            padding: 1.5rem;
        }
        .modal-footer {
            border-top: 1px solid rgba(0,0,0,0.05);
            padding: 1.5rem;
        }
        .form-floating {
            margin-bottom: 1.5rem;
        }
        .form-floating > .form-control {
            padding: 1.5rem 0.75rem 0.75rem;
            height: calc(3.5rem + 2px);
        }
        .form-floating > label {
            padding: 1rem 0.75rem;
        }
        .form-select {
            padding: 0.75rem;
            height: calc(3.5rem + 2px);
        }
        /* Toast 提示样式 */
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1060;
        }
        .toast {
            width: 350px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border: none;
            margin-bottom: 10px;
            overflow: hidden;
        }
        .toast-header {
            border-bottom: none;
            padding: 0.75rem 1rem;
        }
        .toast-body {
            padding: 1rem;
        }
    " />
    <jsp:param name="scripts" value="
        <script>
            // 创建Toast提示
            function showToast(message, type = 'success') {
                const toastContainer = document.querySelector('.toast-container');
                if (!toastContainer) return;

                const toastId = 'toast-' + Date.now();
                const iconClass = type === 'success' ? 'bi-check-circle-fill text-success' : 'bi-exclamation-triangle-fill text-danger';
                const title = type === 'success' ? '成功' : '错误';

                // 使用DOM API创建元素，避免JSP解析问题
                const toastDiv = document.createElement('div');
                toastDiv.id = toastId;
                toastDiv.className = 'toast';
                toastDiv.setAttribute('role', 'alert');
                toastDiv.setAttribute('aria-live', 'assertive');
                toastDiv.setAttribute('aria-atomic', 'true');

                const toastHeader = document.createElement('div');
                toastHeader.className = 'toast-header';

                const icon = document.createElement('i');
                icon.className = 'bi ' + iconClass + ' me-2';

                const strong = document.createElement('strong');
                strong.className = 'me-auto';
                strong.textContent = title;

                const small = document.createElement('small');
                small.textContent = '刚刚';

                const closeButton = document.createElement('button');
                closeButton.className = 'btn-close';
                closeButton.setAttribute('data-bs-dismiss', 'toast');
                closeButton.setAttribute('aria-label', 'Close');

                const toastBody = document.createElement('div');
                toastBody.className = 'toast-body';
                toastBody.textContent = message;

                toastHeader.appendChild(icon);
                toastHeader.appendChild(strong);
                toastHeader.appendChild(small);
                toastHeader.appendChild(closeButton);

                toastDiv.appendChild(toastHeader);
                toastDiv.appendChild(toastBody);

                toastContainer.appendChild(toastDiv);
                const toastElement = document.getElementById(toastId);
                const toast = new bootstrap.Toast(toastElement, { delay: 5000 });
                toast.show();

                // 自动移除
                toastElement.addEventListener('hidden.bs.toast', function() {
                    toastElement.remove();
                });
            }

            // 页面加载完成后执行
            document.addEventListener('DOMContentLoaded', function() {
                // 初始化表单验证
                const forms = document.querySelectorAll('.needs-validation');
                Array.from(forms).forEach(form => {
                    form.addEventListener('submit', event => {
                        if (!form.checkValidity()) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });

                // 初始化搜索功能
                const searchInput = document.getElementById('cameraSearchInput');
                if (searchInput) {
                    searchInput.addEventListener('keyup', function() {
                        filterCameras();
                    });
                }

                // 筛选按钮点击事件
                const filterButtons = document.querySelectorAll('.filter-btn[data-filter]');
                filterButtons.forEach(btn => {
                    btn.addEventListener('click', function() {
                        // 移除同组按钮的active类
                        document.querySelectorAll('.filter-btn[data-filter]').forEach(b => b.classList.remove('active'));
                        // 添加当前按钮的active类
                        this.classList.add('active');
                        // 筛选摄像头
                        filterCameras();
                    });
                });

                // 位置筛选按钮点击事件
                const locationButtons = document.querySelectorAll('.filter-btn[data-location]');
                locationButtons.forEach(btn => {
                    btn.addEventListener('click', function() {
                        // 移除同组按钮的active类
                        document.querySelectorAll('.filter-btn[data-location]').forEach(b => b.classList.remove('active'));
                        // 添加当前按钮的active类
                        this.classList.add('active');
                        // 筛选摄像头
                        filterCameras();
                    });
                });

                // 筛选摄像头函数
                function filterCameras() {
                    const searchText = searchInput ? searchInput.value.toLowerCase() : '';
                    const statusFilter = document.querySelector('.filter-btn[data-filter].active').getAttribute('data-filter');
                    const locationFilter = document.querySelector('.filter-btn[data-location].active').getAttribute('data-location');

                    const cameraItems = document.querySelectorAll('.camera-item');

                    cameraItems.forEach(item => {
                        const cameraName = item.querySelector('.camera-name').textContent.toLowerCase();
                        const cameraLocation = item.querySelector('.camera-location').textContent.toLowerCase();
                        const cameraStatus = item.getAttribute('data-status');
                        const cameraLocationAttr = item.getAttribute('data-location');

                        // 检查是否匹配搜索文本
                        const matchesSearch = cameraName.includes(searchText) || cameraLocation.includes(searchText);

                        // 检查是否匹配状态筛选
                        const matchesStatus = statusFilter === 'all' || cameraStatus === statusFilter;

                        // 检查是否匹配位置筛选
                        const matchesLocation = locationFilter === 'all' || cameraLocationAttr === locationFilter;

                        // 显示或隐藏摄像头
                        if (matchesSearch && matchesStatus && matchesLocation) {
                            item.style.display = '';
                        } else {
                            item.style.display = 'none';
                        }
                    });

                    // 检查是否有可见的摄像头
                    // 使用filter而不是属性选择器，避免JSP解析问题
                    const allCameras = document.querySelectorAll('.camera-item');
                    const visibleCameras = Array.from(allCameras).filter(item => item.style.display === 'none');
                    const noResultsElement = document.getElementById('noResultsMessage');

                    if (visibleCameras.length === cameraItems.length) {
                        // 如果没有可见的摄像头，显示无结果消息
                        if (!noResultsElement) {
                            const cameraList = document.getElementById('cameraList');
                            // 创建元素而不是使用HTML字符串，避免JSP解析问题
                            const noResultsDiv = document.createElement('div');
                            noResultsDiv.id = 'noResultsMessage';
                            noResultsDiv.className = 'col-12';

                            const alertDiv = document.createElement('div');
                            alertDiv.className = 'alert alert-info text-center py-4 rounded-4 shadow-sm';

                            const icon = document.createElement('i');
                            icon.className = 'bi bi-search fs-1 mb-3 d-block';

                            const heading = document.createElement('h4');
                            heading.textContent = '未找到匹配的摄像头';

                            const paragraph = document.createElement('p');
                            paragraph.className = 'mb-0';
                            paragraph.textContent = '请尝试调整搜索条件';

                            alertDiv.appendChild(icon);
                            alertDiv.appendChild(heading);
                            alertDiv.appendChild(paragraph);
                            noResultsDiv.appendChild(alertDiv);

                            cameraList.appendChild(noResultsDiv);
                        }
                    } else if (noResultsElement) {
                        // 如果有可见的摄像头，移除无结果消息
                        noResultsElement.remove();
                    }
                }

                // 添加摄像头表单提交
                const addCameraForm = document.getElementById('addCameraForm');
                if (addCameraForm) {
                    addCameraForm.addEventListener('submit', function(e) {
                        e.preventDefault();

                        if (!this.checkValidity()) {
                            e.stopPropagation();
                            this.classList.add('was-validated');
                            return;
                        }

                        // 获取表单数据
                        const formData = new FormData(this);
                        const formDataObj = {};
                        formData.forEach((value, key) => {
                            formDataObj[key] = value;
                        });

                        // 发送AJAX请求
                        fetch('${pageContext.request.contextPath}/camera/add', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                            },
                            body: new URLSearchParams(formDataObj).toString()
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                // 关闭模态框
                                const modal = bootstrap.Modal.getInstance(document.getElementById('addCameraModal'));
                                modal.hide();

                                // 显示成功提示
                                showToast(data.message, 'success');

                                // 延迟刷新页面
                                setTimeout(() => {
                                    window.location.reload();
                                }, 1000);
                            } else {
                                showToast(data.message || '添加失败', 'error');
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            showToast('添加失败，请稍后重试', 'error');
                        });
                    });
                }

                // 连接摄像头
                window.connectCamera = function(cameraId) {
                    fetch('${pageContext.request.contextPath}/camera/control', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: 'cameraId=' + cameraId + '&action=connect'
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            showToast(data.message, 'success');
                            // 延迟刷新页面
                            setTimeout(() => {
                                window.location.reload();
                            }, 1000);
                        } else {
                            showToast(data.message || '连接失败', 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        showToast('连接失败，请稍后重试', 'error');
                    });
                };

                // 断开摄像头
                window.disconnectCamera = function(cameraId) {
                    fetch('${pageContext.request.contextPath}/camera/control', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: 'cameraId=' + cameraId + '&action=disconnect'
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            showToast(data.message, 'success');
                            // 延迟刷新页面
                            setTimeout(() => {
                                window.location.reload();
                            }, 1000);
                        } else {
                            showToast(data.message || '断开失败', 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        showToast('断开失败，请稍后重试', 'error');
                    });
                };

                // 查看视频流
                window.viewStream = function(cameraId) {
                    window.location.href = '${pageContext.request.contextPath}/camera/stream?id=' + cameraId;
                };

                // 查看摄像头详情
                window.viewCameraDetail = function(cameraId) {
                    window.location.href = '${pageContext.request.contextPath}/camera/detail?id=' + cameraId;
                };

                // 流服务器管理功能
                window.checkStreamServerStatus = function() {
                    fetch('${pageContext.request.contextPath}/stream-server/manage?action=status')
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                updateServerStatusIndicator(data.isRunning);
                                showToast('服务器状态检查完成: ' + data.message, 'success');
                            } else {
                                showToast('检查服务器状态失败: ' + data.message, 'error');
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            showToast('检查服务器状态失败，请稍后重试', 'error');
                        });
                };

                window.startStreamServer = function() {
                    if (confirm('确定要启动视频流服务器吗？')) {
                        showToast('正在启动视频流服务器...', 'success');

                        fetch('${pageContext.request.contextPath}/stream-server/manage', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                            },
                            body: 'action=start'
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                updateServerStatusIndicator(true);
                                showToast(data.message, 'success');
                            } else {
                                showToast('启动失败: ' + data.message, 'error');
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            showToast('启动失败，请稍后重试', 'error');
                        });
                    }
                };

                window.stopStreamServer = function() {
                    if (confirm('确定要停止视频流服务器吗？这将中断所有正在进行的视频流。')) {
                        showToast('正在停止视频流服务器...', 'success');

                        fetch('${pageContext.request.contextPath}/stream-server/manage', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                            },
                            body: 'action=stop'
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                updateServerStatusIndicator(false);
                                showToast(data.message, 'success');
                            } else {
                                showToast('停止失败: ' + data.message, 'error');
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            showToast('停止失败，请稍后重试', 'error');
                        });
                    }
                };

                window.restartStreamServer = function() {
                    if (confirm('确定要重启视频流服务器吗？这将暂时中断所有正在进行的视频流。')) {
                        showToast('正在重启视频流服务器...', 'success');

                        fetch('${pageContext.request.contextPath}/stream-server/manage', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                            },
                            body: 'action=restart'
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                updateServerStatusIndicator(true);
                                showToast(data.message, 'success');
                            } else {
                                showToast('重启失败: ' + data.message, 'error');
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            showToast('重启失败，请稍后重试', 'error');
                        });
                    }
                };

                window.showStreamServerDetails = function() {
                    fetch('${pageContext.request.contextPath}/stream-server/manage?action=status')
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                const details = data.healthDetails || '无详细信息';
                                alert('视频流服务器详细信息:' + String.fromCharCode(10) + String.fromCharCode(10) + details);
                            } else {
                                showToast('获取详细信息失败: ' + data.message, 'error');
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            showToast('获取详细信息失败，请稍后重试', 'error');
                        });
                };

                function updateServerStatusIndicator(isRunning) {
                    const indicator = document.getElementById('serverStatusIndicator');
                    if (indicator) {
                        indicator.className = 'badge ' + (isRunning ? 'bg-success' : 'bg-danger') + ' rounded-pill px-3 py-2';
                        const iconClass = isRunning ? 'check-circle-fill' : 'x-circle-fill';
                        const statusText = isRunning ? '运行中' : '未运行';
                        indicator.innerHTML = '<i class=\"bi bi-' + iconClass + ' me-1\"></i>' + statusText;
                    }
                }

                // 添加动画效果
                const cameraCards = document.querySelectorAll('.camera-card');
                cameraCards.forEach((card, index) => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';

                    setTimeout(() => {
                        card.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100 + index * 50);
                });

                // 统计卡片动画
                const statsCards = document.querySelectorAll('.stats-card');
                statsCards.forEach((card, index) => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';

                    setTimeout(() => {
                        card.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100 + index * 100);
                });
            });
        </script>
    " />
</jsp:include>
